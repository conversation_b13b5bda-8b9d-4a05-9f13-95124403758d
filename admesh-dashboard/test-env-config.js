// Test environment configuration
const { config, getCurrentEnvironment, isTest } = require('./src/config/environment.ts');

console.log('🔧 Testing Environment Configuration');
console.log('=====================================');
console.log('Environment Variables:');
console.log('  NEXT_PUBLIC_ENVIRONMENT:', process.env.NEXT_PUBLIC_ENVIRONMENT);
console.log('  NEXT_PUBLIC_API_BASE_URL:', process.env.NEXT_PUBLIC_API_BASE_URL);
console.log('');
console.log('Detected Environment:', getCurrentEnvironment());
console.log('Is Test Environment:', isTest());
console.log('');
console.log('Configuration:');
console.log('  Environment:', config.environment);
console.log('  API Base URL:', config.api.baseUrl);
console.log('  Firebase Project:', config.firebase.projectId);
console.log('  Debug Mode:', config.features.debugging);
console.log('');

if (config.api.baseUrl === 'http://127.0.0.1:8000') {
  console.log('✅ SUCCESS: API Base URL is correctly set to local server');
} else {
  console.log('❌ ERROR: API Base URL is not set to local server');
  console.log('   Expected: http://127.0.0.1:8000');
  console.log('   Actual:', config.api.baseUrl);
}
