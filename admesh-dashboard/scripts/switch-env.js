#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const args = process.argv.slice(2);
const environment = args[0];

if (!environment || !['dev', 'prod', 'production', 'test'].includes(environment)) {
  console.log('❌ Please specify environment: dev, prod, or test');
  console.log('Usage: node scripts/switch-env.js [dev|prod|test]');
  console.log('  dev   - Development environment (localhost:8000)');
  console.log('  prod  - Production environment (api.useadmesh.com)');
  console.log('  test  - Test environment (localhost:8000 with production build)');
  process.exit(1);
}

let envFile;
if (environment === 'dev') {
  envFile = '.env.dev';
} else if (environment === 'test') {
  envFile = '.env.test';
} else {
  envFile = '.env.production';
}
const envPath = path.join(__dirname, '..', envFile);
const targetPath = path.join(__dirname, '..', '.env');

if (!fs.existsSync(envPath)) {
  console.log(`❌ Environment file ${envFile} not found`);
  process.exit(1);
}

try {
  fs.copyFileSync(envPath, targetPath);
  
  // Read the environment file to show current config
  const envContent = fs.readFileSync(targetPath, 'utf8');
  const apiUrl = envContent.match(/NEXT_PUBLIC_API_BASE_URL=(.+)/)?.[1];
  const env = envContent.match(/NEXT_PUBLIC_ENVIRONMENT=(.+)/)?.[1];
  
  console.log('✅ Environment switched successfully!');
  console.log(`🌍 Environment: ${env}`);
  console.log(`🔗 API URL: ${apiUrl}`);
  console.log('');
  console.log('Available commands:');
  console.log('  npm run dev        - Start development server');
  console.log('  npm run build      - Build for production');
  console.log('  npm run start      - Start production server');
  console.log('  npm run test       - Build and start test server (localhost API)');
  console.log('  npm run env:dev    - Switch to development environment');
  console.log('  npm run env:prod   - Switch to production environment');
  console.log('  npm run env:test   - Switch to test environment');
  
} catch (error) {
  console.log('❌ Error switching environment:', error.message);
  process.exit(1);
}
