# Environment Setup Guide

This project supports multiple environment configurations to handle different deployment scenarios.

## Environment Files

- **`.env.dev`** - Development environment (localhost:8000)
- **`.env.test`** - Test environment (localhost:8000 with production build)
- **`.env.production`** - Production deployment (api.useadmesh.com)

## Quick Commands

### For Local Testing of Production Builds (what you want)
```bash
# This will use localhost:8000 for API calls with production build
npm run env:test && npm run build && npm run start
# OR use the shorthand:
npm run test
```

### For Development
```bash
npm run dev
```

### For Actual Production Deployment
```bash
# This will use api.useadmesh.com for API calls
npm run env:prod && npm run build && npm run start
```

## Environment Switching

Use the environment switching commands to change configurations:

```bash
npm run env:dev    # Switch to development
npm run env:test   # Switch to test environment (local testing)
npm run env:prod   # Switch to production deployment
```

## Port-Specific Commands

For running on specific ports:

```bash
npm run test:3002   # Test environment on port 3002
npm run test:3003   # Test environment on port 3003
```

## API URL Configuration

- **Development**: `http://127.0.0.1:8000`
- **Test**: `http://127.0.0.1:8000`
- **Production**: `https://api.useadmesh.com`

The test environment allows you to test production builds locally while still connecting to your local backend server.
