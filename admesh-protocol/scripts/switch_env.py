#!/usr/bin/env python3
"""
Environment switching script for AdMesh Protocol
Switches between development, test, and production environments
"""

import os
import sys
import shutil
import argparse
from pathlib import Path


def get_project_root():
    """Get the project root directory"""
    return Path(__file__).parent.parent


def switch_environment(env: str):
    """Switch to the specified environment"""
    project_root = get_project_root()
    
    # Map environment names to files
    env_files = {
        'dev': '.env.dev',
        'development': '.env.dev',
        'test': '.env.test',
        'prod': '.env.production',
        'production': '.env.production'
    }
    
    if env not in env_files:
        print(f"❌ Invalid environment: {env}")
        print(f"Available environments: {', '.join(set(env_files.keys()))}")
        return False
    
    source_file = project_root / env_files[env]
    target_file = project_root / '.env'
    
    if not source_file.exists():
        print(f"❌ Environment file not found: {source_file}")
        return False
    
    try:
        # Copy the environment file
        shutil.copy2(source_file, target_file)
        
        # Read the environment file to show current config
        with open(target_file, 'r') as f:
            content = f.read()
        
        # Extract key information
        env_value = None
        site_url = None
        debug = None
        
        for line in content.split('\n'):
            if line.startswith('ENV='):
                env_value = line.split('=', 1)[1]
            elif line.startswith('SITE_URL='):
                site_url = line.split('=', 1)[1]
            elif line.startswith('DEBUG='):
                debug = line.split('=', 1)[1]
        
        print('✅ Environment switched successfully!')
        print(f'🌍 Environment: {env_value}')
        print(f'🔗 Site URL: {site_url}')
        print(f'🐛 Debug: {debug}')
        print('')
        print('Available commands:')
        print('  python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000')
        print('  python scripts/validate_environment.py')
        print('')
        print('Environment switching commands:')
        print('  python scripts/switch_env.py dev    - Switch to development')
        print('  python scripts/switch_env.py test   - Switch to test environment')
        print('  python scripts/switch_env.py prod   - Switch to production')
        
        return True
        
    except Exception as e:
        print(f'❌ Error switching environment: {e}')
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Switch AdMesh Protocol environment')
    parser.add_argument('environment', 
                       choices=['dev', 'development', 'test', 'prod', 'production'],
                       help='Environment to switch to')
    
    args = parser.parse_args()
    
    print(f"🔄 Switching to {args.environment} environment...")
    
    if switch_environment(args.environment):
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
